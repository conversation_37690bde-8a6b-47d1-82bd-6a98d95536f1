/* Test Details Modal Styles - Restored Original Design */

/* Modal overlay */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-in-out;
}

.modal.show {
    display: block;
}

/* Modal content container */
.modal-content {
    background-color: #ffffff;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modal header */
.modal-header {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
    padding: 24px 32px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.02em;
}

/* Close button */
.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    font-weight: 300;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

/* Modal body */
.modal-body {
    padding: 24px 32px 32px;
}

/* Test details grid */
.test-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

/* Detail items */
.detail-item {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #0078d4;
    transition: all 0.2s ease;
}

.detail-item:hover {
    background-color: #f3f2f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item strong {
    display: block;
    color: #323130;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    opacity: 0.8;
}

.detail-item span {
    display: block;
    color: #201f1e;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.4;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-passed {
    background-color: #dff6dd;
    color: #107c10;
    border: 1px solid #107c10;
}

.status-badge.status-failed {
    background-color: #fde7e9;
    color: #e81123;
    border: 1px solid #e81123;
}

.status-badge.status-running {
    background-color: #fff4ce;
    color: #797673;
    border: 1px solid #797673;
}

.status-badge.status-queued {
    background-color: #e1f5fe;
    color: #0078d4;
    border: 1px solid #0078d4;
}

.status-badge.status-unknown {
    background-color: #f3f2f1;
    color: #605e5c;
    border: 1px solid #605e5c;
}

/* Primary button */
.ms-Button--primary {
    background-color: #0078d4;
    border: 1px solid #0078d4;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    display: inline-block;
}

.ms-Button--primary:hover {
    background-color: #106ebe;
    border-color: #106ebe;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
}

/* Loading spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.loading-text {
    color: #605e5c;
    font-size: 16px;
    margin: 0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
        max-width: calc(100% - 20px);
        max-height: calc(100vh - 20px);
    }
    
    .test-details-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .modal-header {
        padding: 20px 24px 12px;
    }
    
    .modal-body {
        padding: 20px 24px 24px;
    }
    
    .detail-item {
        padding: 12px;
    }
}



