/**
 * Test Details Modal
 * Handles the display of detailed test information in a modal dialog
 */

class TestDetailsModal {
    constructor() {
        this.modal = null;
        this.isVisible = false;
        this.currentTsnId = null;
        
        this.init();
    }
    
    init() {
        // Create modal with our original beautiful design
        const modalHtml = `
            <div id="test-details-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">Test Details</h3>
                        <button class="close-btn" type="button" aria-label="Close modal">&times;</button>
                    </div>
                    <div class="modal-body" id="modal-body">
                        <!-- Content will be populated dynamically -->
                    </div>
                </div>
            </div>
        `;

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('test-details-modal');

        // Event listeners
        const closeBtn = this.modal.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => this.hide());
        
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.hide();
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.hide();
            }
        });
    }

    /**
     * Get user credentials from various sources
     * @returns {Object} credentials object with uid and password
     */
    getCredentials() {
        let credentials = { uid: null, password: null };

        // Try to get from window.apiService first
        if (window.apiService && window.apiService.credentials && window.apiService.credentials.uid) {
            credentials = window.apiService.credentials;
            console.log('Using credentials from API service:', credentials.uid);
            return credentials;
        }

        // Try to get from appState
        if (window.appState && window.appState.credentials && window.appState.credentials.uid) {
            credentials = window.appState.credentials;
            console.log('Using credentials from appState:', credentials.uid);
            return credentials;
        }

        // Try to get from session storage
        const sessionUid = sessionStorage.getItem('smarttest_uid');
        const sessionPwd = sessionStorage.getItem('smarttest_pwd');
        if (sessionUid && sessionPwd) {
            credentials = { uid: sessionUid, password: sessionPwd };
            console.log('Using credentials from session storage:', sessionUid);
            return credentials;
        }

        // Try to get from localStorage
        try {
            const storedCredentials = localStorage.getItem('userCredentials');
            if (storedCredentials) {
                credentials = JSON.parse(storedCredentials);
                console.log('Using credentials from localStorage:', credentials.uid);
                return credentials;
            }
        } catch (error) {
            console.warn('Error parsing stored credentials:', error);
        }

        console.warn('No credentials found in any source');
        return credentials;
    }

    /**
     * Format date/time for display
     * @param {string} dateTime - ISO date string
     * @returns {string} formatted date/time
     */
    formatDateTime(dateTime) {
        if (!dateTime) return 'N/A';
        
        try {
            const date = new Date(dateTime);
            if (isNaN(date.getTime())) return 'Invalid Date';
            
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        } catch (error) {
            console.warn('Error formatting date:', error);
            return 'Invalid Date';
        }
    }

    /**
     * Calculate duration between start and end times
     * @param {string} startTime - ISO start time
     * @param {string} endTime - ISO end time
     * @returns {string} formatted duration
     */
    calculateDuration(startTime, endTime) {
        if (!startTime) return 'N/A';
        if (!endTime) return 'Running...';
        
        try {
            const start = new Date(startTime);
            const end = new Date(endTime);
            
            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                return 'Invalid Duration';
            }
            
            const diffMs = end.getTime() - start.getTime();
            const diffSecs = Math.floor(diffMs / 1000);
            const diffMins = Math.floor(diffSecs / 60);
            const diffHours = Math.floor(diffMins / 60);
            
            const hours = diffHours;
            const minutes = diffMins % 60;
            const seconds = diffSecs % 60;
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        } catch (error) {
            console.warn('Error calculating duration:', error);
            return 'Invalid Duration';
        }
    }

    async show(tsnId) {
        this.currentTsnId = tsnId;
        
        // Update title
        const title = this.modal.querySelector('#modal-title');
        title.textContent = `Test Details - TSN ${tsnId}`;
        
        // Show loading state
        this.showLoading();
        
        // Show modal with animation
        this.modal.style.display = 'block';
        this.modal.classList.add('show');

        try {
            // Get credentials
            const credentials = this.getCredentials();
            if (!credentials.uid || !credentials.password) {
                throw new Error('No credentials available. Please log in first.');
            }

            let testDetails = null;

            // Try external API first since it's faster and more reliable
            console.log('Attempting to load test details from external API first...');
            try {
                if (typeof loadTestDetailsFromExternalApi === 'function') {
                    testDetails = await loadTestDetailsFromExternalApi(tsnId, credentials);
                    console.log('Successfully loaded from external API:', testDetails);
                }
            } catch (externalError) {
                console.log('External API failed, trying other APIs:', externalError.message);

                // Try optimized config API service as fallback
                if (window.configApiService) {
                    try {
                        console.log('Using optimized ConfigApiService as fallback...');
                        testDetails = await window.configApiService.getTestDetails(tsnId, credentials);
                        console.log('Successfully loaded from ConfigApiService:', testDetails);
                    } catch (configError) {
                        console.log('ConfigApiService failed, trying database API:', configError.message);

                        // Database API as last resort
                        try {
                            console.log('Attempting to load test details from database API...');
                            if (typeof loadTestDetailsFromDatabaseApi === 'function') {
                                testDetails = await loadTestDetailsFromDatabaseApi(tsnId, credentials);
                                console.log('Successfully loaded from database API:', testDetails);
                            }
                        } catch (dbError) {
                            console.error('All APIs failed:', dbError.message);
                            throw new Error(`Failed to load test details: ${externalError.message}`);
                        }
                    }
                } else {
                    // Fallback if ConfigApiService not available
                    console.log('ConfigApiService not available, trying database API...');
                    try {
                        if (typeof loadTestDetailsFromDatabaseApi === 'function') {
                            testDetails = await loadTestDetailsFromDatabaseApi(tsnId, credentials);
                            console.log('Successfully loaded from database API:', testDetails);
                        }
                    } catch (dbError) {
                        console.error('All APIs failed:', dbError.message);
                        throw new Error(`Failed to load test details: ${externalError.message}`);
                    }
                }
            }

            if (testDetails) {
                this.showTestDetails(testDetails);
            } else {
                throw new Error('No test details returned from API');
            }

        } catch (error) {
            console.error('Error loading test details:', error);
            this.showError('Error loading test details: ' + error.message);
        }
    }
    
    hide() {
        this.modal.style.display = 'none';
        this.modal.classList.remove('show');
        this.currentTsnId = null;
    }
    
    showLoading() {
        const modalBody = this.modal.querySelector('#modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div class="loading-spinner"></div>
                <p class="loading-text">Loading test details...</p>
            </div>
        `;
    }
    
    showError(message) {
        const modalBody = this.modal.querySelector('#modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <p style="color: #d13438; font-size: 16px; margin: 0;">${message}</p>
            </div>
        `;
    }
    
    showTestDetails(testDetails) {
        const modalBody = this.modal.querySelector('#modal-body');
        
        const statusClass = this.getStatusClass(testDetails.status);
        const duration = testDetails.duration || this.calculateDuration(testDetails.start_time, testDetails.end_time);
        
        modalBody.innerHTML = `
            <div class="test-details-grid">
                <div class="detail-item">
                    <strong>Test Case ID:</strong>
                    <span>${testDetails.tc_id || testDetails.test_id || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Name:</strong>
                    <span>${testDetails.test_name || testDetails.name || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Status:</strong>
                    <span class="status-badge status-${statusClass}">${testDetails.status || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Environment:</strong>
                    <span>${testDetails.environment || testDetails.envir || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Start Time:</strong>
                    <span>${this.formatDateTime(testDetails.start_time)}</span>
                </div>
                <div class="detail-item">
                    <strong>End Time:</strong>
                    <span>${this.formatDateTime(testDetails.end_time) || 'Running...'}</span>
                </div>
                <div class="detail-item">
                    <strong>Duration:</strong>
                    <span>${duration}</span>
                </div>
                <div class="detail-item">
                    <strong>User:</strong>
                    <span>${testDetails.user || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Passed Cases:</strong>
                    <span style="color: #107c10; font-weight: 600;">${testDetails.passed_cases || 0}</span>
                </div>
                <div class="detail-item">
                    <strong>Failed Cases:</strong>
                    <span style="color: #e81123; font-weight: 600;">${testDetails.failed_cases || 0}</span>
                </div>
                <div class="detail-item">
                    <strong>Total Cases:</strong>
                    <span>${testDetails.total_cases || 0}</span>
                </div>
                <div class="detail-item">
                    <strong>Session ID:</strong>
                    <span>${testDetails.tsn_id}</span>
                </div>
                ${testDetails.comments ? `
                    <div class="detail-item full-width">
                        <strong>Comments:</strong>
                        <span>${testDetails.comments}</span>
                    </div>
                ` : ''}
                ${testDetails.details_url ? `
                    <div class="detail-item full-width">
                        <strong>Full Report:</strong>
                        <a href="${testDetails.details_url}" target="_blank" class="ms-Button ms-Button--primary">
                            View Full Report
                        </a>
                    </div>
                ` : ''}
                ${this.renderParameters(testDetails.originalParameters)}
            </div>
        `;
    }

    renderParameters(parameters) {
        if (!parameters || Object.keys(parameters).length === 0) {
            return '';
        }

        const paramHtml = Object.entries(parameters)
            .map(([key, value]) => `
                <div class="detail-item">
                    <strong>${key}:</strong>
                    <span>${value}</span>
                </div>
            `).join('');

        return paramHtml;
    }

    getStatusClass(status) {
        if (!status) return 'unknown';
        const s = status.toLowerCase();
        if (s === 'success' || s === 'passed' || s === 'completed') return 'passed';
        if (s === 'failed' || s === 'error') return 'failed';
        if (s === 'running') return 'running';
        if (s === 'queued') return 'queued';
        return 'unknown';
    }
    
    // Public method to check if modal is visible
    isOpen() {
        return this.isVisible;
    }
    
    // Public method to get current TSN ID
    getCurrentTsnId() {
        return this.currentTsnId;
    }
}

// Initialize the modal when the script loads
document.addEventListener('DOMContentLoaded', () => {
    window.testDetailsModal = new TestDetailsModal();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    window.testDetailsModal = new TestDetailsModal();
}



