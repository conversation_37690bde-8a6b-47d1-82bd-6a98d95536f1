✅ Login successful
reports.details-external.js:48 📡 Fetching report summary...
reports.details-external.js:63 🔑 External API session available: Explicit ID
reports.details-external.js:68 📡 Making request with credentials: include to ensure cookies are sent
reports.details-external.js:71 📡 Summary response status: 200
reports.details-external.js:72 📡 Summary response headers: {access-control-allow-origin: '*', connection: 'keep-alive', content-length: '1469', content-type: 'text/html; charset=utf-8', cross-origin-resource-policy: 'same-origin', …}
reports.details-external.js:142 📄 Summary HTML length: 1469
reports.details-external.js:250 ❌ EXTERNAL API ONLY MODE - Error in loadTestDetailsFromExternalApi for 17151: parseReportSummaryHtml is not defined ReferenceError: parseReportSummaryHtml is not defined
    at loadTestDetailsFromExternalApi (reports.details-external.js:143:29)
    at async TestDetailsModal.show (test-details-modal.js:223:39)
loadTestDetailsFromExternalApi @ reports.details-external.js:250
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:223
await in show
handleViewDetailsClick @ config.js:840
reports.details-external.js:251 🚫 NO DATABASE FALLBACK - External API must be fixed to load test details
loadTestDetailsFromExternalApi @ reports.details-external.js:251
await in loadTestDetailsFromExternalApi
show @ test-details-modal.js:223
await in show
handleViewDetailsClick @ config.js:840
test-details-modal.js:235 Error loading test details: Error: EXTERNAL API ONLY: Failed to load details for 17151 from external API: parseReportSummaryHtml is not defined. Database fallback is disabled.
    at loadTestDetailsFromExternalApi (reports.details-external.js:254:15)
    at async TestDetailsModal.show (test-details-modal.js:223:39)
show @ test-details-modal.js:235
await in show
handleViewDetailsClick @ config.js:840
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
4simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
2simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
3simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
3simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
2simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
4simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
4simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
2simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
3simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
unified-api-service.js:259 Constructing URL: Base=http://localhost:3000, Endpoint=/local/recent-runs, Full URL=http://localhost:3000/local/recent-runs
unified-api-service.js:264 Making GET request to: http://localhost:3000/local/recent-runs
3simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":50,"type":"single_case"}
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":50,"type":"single_case"}
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
config.js:375 Received 50 recent runs update
config.js:660 renderRecentRuns called with data: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
config.js:661 recentRunsBody element: <tbody id=​"recent-runs-body">​…​</tbody>​
config.js:679 Rendering 50 recent runs
simple-optimizations.js:32 Request deduplication: recentRuns_{"limit":100}
simple-optimizations.js:47 Request completed: recentRuns_{"limit":100}
6simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
config.js:384 Received 0 active tests update
config.js:436 Updating active tests display with 0 tests
simple-optimizations.js:26 Cache HIT: recentRuns_{"limit":100}
simple-optimizations.js:280 Found 0 active tests from 100 recent runs
config.js:384 Received 0 active tests update