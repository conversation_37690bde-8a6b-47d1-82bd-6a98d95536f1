PS C:\Dev\smarttest> npm start    

> smarttest@1.0.0 start
> node frontend/server/api.js

Loaded environment variables from .env
Initializing API server...
No valid environment specified, using default environment: qa02
Using direct SSH connection (fixed implementation)
Database connection initialized
Server running on port 3000
[2025-07-18T10:45:05.426Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::ffff:127.0.0.1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:45:07.404Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
[2025-07-18T10:45:13.812Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:45:16.475Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
[2025-07-18T10:45:19.409Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::ffff:127.0.0.1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:45:21.474Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
[2025-07-18T10:45:22.193Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:45:22.196Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
[2025-07-18T10:45:26.466Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-18T10:45:31.471Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:45:33.937Z] GET /local/test-details/17151?uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/test-details/:tsn_id
Fetching test details for tsn_id: 17151
[DEBUG] Database connection status: {
  connected: false,
  threadId: null,
  database: undefined,
  connectionId: null
}
[DEBUG] Fetching test session details for tsn_id: 17151
[2025-07-18T10:45:36.197Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:45:36.481Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
[2025-07-18T10:45:38.211Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[DEBUG] getTestSessionDetails: About to query test_result for tsn_id: 17151
[2025-07-18T10:45:41.475Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
[DEBUG] getTestSessionDetails: test_result query for tsn_id: 17151 returned 3 rows.
[DEBUG] getTestSessionDetails: First row from test_result: {"tc_id":"2369","seq_index":"1","outcome":"P","creation_time":"2025-07-18 10:12:14.663","cnt":"21814066","test_case_name":"NULL","description":"MLP payout","input_data":"ssh jps-qa28-app01 cat /home/<USER>/TA/qa02_${version}properties","output_data":"<properties>\\n\\n<env>http://jps-qa02-pool.lab.wagerworks.com</env>\\n<login>jpsadmin</login>\\n<password>password</password>\\n<env_meter>http://jps-qa02-meter.lab.wagerworks.com</env_meter>\\n\\n<jps_jdbc_username>jpsqa02</jps_jdbc_username>\\n<jps_jdbc_password>jpsqa02</jps_jdbc_password>\\n<jps_jdbc_url>***********************************************************</jps_jdbc_url>\\n<jps_jdbc_driver>org.postgresql.Driver</jps_jdbc_driver>\\n<jdbc_driver>org.postgresql.Driver</jdbc_driver>\\n<jdbc_username>jpsqa02</jdbc_username>\\n<jdbc_password>jpsqa02</jdbc_password>\\n<jdbc_url>***********************************************************</jdbc_url>\\n\\n<systemId>11182</systemId>\\n<systemId1>11183</systemId1>\\n<systemId2>11184</systemId2>\\n<systemId3>11185</systemId3>\\n<systemId4>11186</systemId4>\\n<systemId5>11187</systemId5>\\n<systemId6>11188</systemId6>\\n<systemId7>11189</systemId7>\\n<systemId8>11190</systemId8>\\n<systemId9>11191</systemId9>\\n\\n<PBGroup>APB1</PBGroup>\\n\\n<MJPgrp>MJP7</MJPgrp>\\n<MJP_FUN_grp>AMJF</MJP_FUN_grp>\\n<MJP_FUN_new>AMJN</MJP_FUN_new>\\n<MJPgrpNew>AMJ1</MJPgrpNew>\\n<MJPgrp_tmlp2>AMJ2</MJPgrp_tmlp2>\\n\\n<MLPGroup>AMLP</MLPGroup>\\n<MLPGroup14>AL14</MLPGroup14>\\n<MLPGroup19>AL19</MLPGroup19>\\n<MLPGroup24>RFLP</MLPGroup24>\\n\\n<MHBgroup>AMHB</MHBgroup>\\n\\n<env_prts>http://mprts-qa01.lab.wagerworks.com:8080</env_prts>\\n<jdbc_prts>*****************************************************</jdbc_prts>\\n<prts_jdbc_username>mprts_ro</prts_jdbc_username>\\n<prts_jdbc_driver>com.mysql.jdbc.Driver</prts_jdbc_driver>\\n\\n<MHBwager1>1000000000</MHBwager1>\\n<MHBwager2>99000</MHBwager2>\\n<MHBwager3>10000000</MHBwager3>\\n\\n<idx_num>8</idx_num>\\n\\n<mprtstenant>10</mprtstenant>\\n\\n<contribution1>9000</contribution1>\\n<contribution2>10000</contribution2>\\n<contribution3>13000</contribution3>\\n<contribution4>13500</contribution4>\\n<contribution5>14000</contribution5>\\n<contribution6>15000</contribution6>\\n<contribution7>20000</contribution7>\\n<contribution8>100000</contribution8>\\n\\n</properties>\\n","error_message":"NULL"}
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:45:46.468Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
[2025-07-18T10:45:50.196Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
Retrieved test details for tsn_id: 17151
Test case count: 3
First test case: {
  "tc_id": "2369",
  "test_case_name": "NULL",
  "description": "MLP payout",
  "outcome": "P",
  "status": "Passed",
  "creation_time": "2025-07-18 10:12:14.663",
  "cnt": "21814066",
  "seq_index": "1",
  "input_data": "ssh jps-qa28-app01 cat /home/<USER>/TA/qa02_${version}properties",
  "output_data": "<properties>\\n\\n<env>http://jps-qa02-pool.lab.wagerworks.com</env>\\n<login>jpsadmin</login>\\n<password>password</password>\\n<env_meter>http://jps-qa02-meter.lab.wagerworks.com</env_meter>\\n\\n<jps_jdbc_username>jpsqa02</jps_jdbc_username>\\n<jps_jdbc_password>jpsqa02</jps_jdbc_password>\\n<jps_jdbc_url>***********************************************************</jps_jdbc_url>\\n<jps_jdbc_driver>org.postgresql.Driver</jps_jdbc_driver>\\n<jdbc_driver>org.postgresql.Driver</jdbc_driver>\\n<jdbc_username>jpsqa02</jdbc_username>\\n<jdbc_password>jpsqa02</jdbc_password>\\n<jdbc_url>***********************************************************</jdbc_url>\\n\\n<systemId>11182</systemId>\\n<systemId1>11183</systemId1>\\n<systemId2>11184</systemId2>\\n<systemId3>11185</systemId3>\\n<systemId4>11186</systemId4>\\n<systemId5>11187</systemId5>\\n<systemId6>11188</systemId6>\\n<systemId7>11189</systemId7>\\n<systemId8>11190</systemId8>\\n<systemId9>11191</systemId9>\\n\\n<PBGroup>APB1</PBGroup>\\n\\n<MJPgrp>MJP7</MJPgrp>\\n<MJP_FUN_grp>AMJF</MJP_FUN_grp>\\n<MJP_FUN_new>AMJN</MJP_FUN_new>\\n<MJPgrpNew>AMJ1</MJPgrpNew>\\n<MJPgrp_tmlp2>AMJ2</MJPgrp_tmlp2>\\n\\n<MLPGroup>AMLP</MLPGroup>\\n<MLPGroup14>AL14</MLPGroup14>\\n<MLPGroup19>AL19</MLPGroup19>\\n<MLPGroup24>RFLP</MLPGroup24>\\n\\n<MHBgroup>AMHB</MHBgroup>\\n\\n<env_prts>http://mprts-qa01.lab.wagerworks.com:8080</env_prts>\\n<jdbc_prts>*****************************************************</jdbc_prts>\\n<prts_jdbc_username>mprts_ro</prts_jdbc_username>\\n<prts_jdbc_driver>com.mysql.jdbc.Driver</prts_jdbc_driver>\\n\\n<MHBwager1>1000000000</MHBwager1>\\n<MHBwager2>99000</MHBwager2>\\n<MHBwager3>10000000</MHBwager3>\\n\\n<idx_num>8</idx_num>\\n\\n<mprtstenant>10</mprtstenant>\\n\\n<contribution1>9000</contribution1>\\n<contribution2>10000</contribution2>\\n<contribution3>13000</contribution3>\\n<contribution4>13500</contribution4>\\n<contribution5>14000</contribution5>\\n<contribution6>15000</contribution6>\\n<contribution7>20000</contribution7>\\n<contribution8>100000</contribution8>\\n\\n</properties>\\n",
  "error_message": "NULL",
  "failure_context": null,
  "source": "database"
}
[2025-07-18T10:45:51.331Z] POST /api/external/login from ::1
POST /api/external/login
[2025-07-18T10:45:51.467Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
[External Login] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login
[External Login] Original cookie: JSESSIONID=DC58F492CDD1F6BD3D7FA0CD006C34B5; Path=/AutoRun; HttpOnly
[External Login] Modified cookie: JSESSIONID=DC58F492CDD1F6BD3D7FA0CD006C34B5; Path=/; HttpOnly
[External Login] Forwarding cookies to client: 1 cookies with fixed Path
[External Login] Extracted JSESSIONID from Set-Cookie: DC58F492CDD1F6BD3D7FA0CD006C34B5
[External Login] Stored JSESSIONID in session for future requests
[External Login] Set additional client-side cookie with path /
[2025-07-18T10:45:53.354Z] GET /api/external/ReportSummary?tsn_id=17151 from ::1
GET /api/external/ReportSummary
[External ReportSummary] Incoming request cookies: JSESSIONID=DC58F492CDD1F6BD3D7FA0CD006C34B5
[External ReportSummary] Extracted JSESSIONID: DC58F492CDD1F6BD3D7FA0CD006C34B5
[External ReportSummary] Forwarding to http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/ReportSummary?tsn_id=17151
[External ReportSummary] Using JSESSIONID: DC58F492CDD1F6BD3D7FA0CD006C34B5
[2025-07-18T10:45:54.198Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[2025-07-18T10:45:56.483Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:46:01.482Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-18T10:46:04.208Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:46:06.472Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
[2025-07-18T10:46:10.207Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
[2025-07-18T10:46:11.475Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
[2025-07-18T10:46:18.208Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:46:18.479Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
[2025-07-18T10:46:26.201Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:46:32.202Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
[2025-07-18T10:46:42.199Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:46:46.204Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:46:58.211Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[2025-07-18T10:47:00.208Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
✅ Database Query: Retrieved 50 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 50 sessions in 10 batches...
✅ Processed 50 sessions successfully
Retrieved 50 recent runs
✅ Database Query: Retrieved 100 test sessions
📊 First 3 FULL rows from database:
Row 1 start_ts: 2025-07-18 10:12:09, end_ts: 2025-07-18 10:12:24
Row 2 start_ts: 2025-07-18 09:42:38, end_ts: 2025-07-18 09:42:53
Row 3 start_ts: 2025-07-18 07:08:56, end_ts: 2025-07-18 07:09:06
⏳ Processing 100 sessions in 20 batches...
✅ Processed 100 sessions successfully
Retrieved 100 recent runs
[2025-07-18T10:47:14.208Z] GET /local/recent-runs?limit=50&type=single_case&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 50, type: 'single_case', uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Adding single test case filter
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     WHERE ts.tc_id IS NOT NULL AND ts.tc_id != "" AND (ts.ts_id IS NULL OR ts.ts_id = "") ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [50]
[2025-07-18T10:47:14.212Z] GET /local/recent-runs?limit=100&uid=yuri.shvartz%40igtplayer.com&password=test from ::1
GET /local/recent-runs
Fetching recent runs with filters: { limit: 100, uid: '<EMAIL>' }
[GET_RECENT_RUNS] No time range filter applied (all or undefined).
[GET_RECENT_RUNS] Final SQL:
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
     ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?
[GET_RECENT_RUNS] Final Params: [100]
[2025-07-18T10:47:18.514Z] GET /local/recent-runs?uid=testuser&password=placeholder from ::1
✅ Database Query: Retrieved 50 test sessions